import pandas as pd
import numpy as np


class VariationCoefficientCalculator:
    """变异系数计算工具类"""

    @staticmethod
    def calculate_cv(data):
        """计算变异系数 (CV = std/mean * 100%)"""
        if len(data) <= 1 or data.mean() == 0:
            return np.nan
        return (data.std() / data.mean()) * 100


class DataProcessor:
    """数据处理器"""

    def __init__(self, data_file='data2024_copy.xlsx'):
        """初始化数据处理器"""
        self.data_file = data_file
        self.df = None

    def load_and_prepare_data(self):
        """加载和预处理数据"""
        print(f"正在加载数据文件: {self.data_file}")

        # 加载原始数据
        self.df = pd.read_excel(self.data_file)
        print(f"原始数据形状: {self.df.shape}")

        # 删除电场强度缺失的行
        self.df = self.df.dropna(subset=['E (kV cm-1)']).copy()
        print(f"清理后数据形状: {self.df.shape}")

        # 计算归一化指标
        self.df['Wrec_norm_10000'] = 10000 * self.df['Wrec (J cm-3)'] / (self.df['E (kV cm-1)'] ** 2)
        self.df['Wrec_norm_1000'] = 1000 * self.df['Wrec (J cm-3)'] / self.df['E (kV cm-1)']

        # 创建分组标签
        self.df['group_label'] = self.df.groupby(['DOI', 'component']).ngroup()

        # 计算分组大小
        group_sizes = self.df.groupby(['DOI', 'component']).size().reset_index(name='group_size')
        self.df = self.df.merge(group_sizes, on=['DOI', 'component'], how='left')

        # 创建分组ID
        self.df['group_id'] = self.df['DOI'].astype(str) + '_' + self.df['component'].astype(str)

        return self.df

    def get_multi_point_groups(self):
        """获取多数据点组"""
        return self.df[self.df['group_size'] > 1]

    def print_basic_statistics(self):
        """打印基本统计信息"""
        total_groups = self.df['group_label'].nunique()
        single_point_groups = len(self.df[self.df['group_size'] == 1]['group_label'].unique())
        multi_point_groups = len(self.df[self.df['group_size'] > 1]['group_label'].unique())

        print(f"\n=== 数据统计信息 ===")
        print(f"总数据点: {len(self.df)}")
        print(f"总分组数: {total_groups}")
        print(f"单数据点分组数: {single_point_groups}")
        print(f"多数据点分组数: {multi_point_groups}")
        print(f"平均每组数据点数: {self.df['group_size'].mean():.2f}")
        print(f"DOI数量: {self.df['DOI'].nunique()}")
        print(f"成分数量: {self.df['component'].nunique()}")


class WithinGroupAnalyzer:
    """组内变异系数分析器"""

    def __init__(self, df):
        """初始化分析器"""
        self.df = df
        self.cv_calculator = VariationCoefficientCalculator()

    def calculate_within_group_cv(self):
        """计算多数据点组的组内变异系数"""
        print("\n=== 计算组内变异系数 ===")

        # 筛选多数据点组
        multi_point_data = self.df[self.df['group_size'] > 1].copy()

        if len(multi_point_data) == 0:
            print("没有多数据点组")
            return None

        # 计算每组的变异系数
        def calculate_group_cv(group):
            """计算单个组的变异系数"""
            if len(group) <= 1:
                return pd.Series({
                    'cv_wrec_norm_10000': np.nan,
                    'cv_wrec_norm_1000': np.nan,
                    'cv_eta': np.nan,
                    'cv_wrec': np.nan,
                    'group_size': len(group)
                })

            return pd.Series({
                'cv_wrec_norm_10000': self.cv_calculator.calculate_cv(group['Wrec_norm_10000']),
                'cv_wrec_norm_1000': self.cv_calculator.calculate_cv(group['Wrec_norm_1000']),
                'cv_eta': self.cv_calculator.calculate_cv(group['η']),
                'cv_wrec': self.cv_calculator.calculate_cv(group['Wrec (J cm-3)']),
                'group_size': len(group)
            })

        # 按组计算变异系数
        within_group_cv = multi_point_data.groupby(['DOI', 'component']).apply(
            calculate_group_cv, include_groups=False
        ).reset_index()

        # 计算平均变异系数
        avg_cv_stats = {
            '10000*Wrec/(E²)': within_group_cv['cv_wrec_norm_10000'].mean(),
            '1000*Wrec/E': within_group_cv['cv_wrec_norm_1000'].mean(),
            'η (储能效率)': within_group_cv['cv_eta'].mean(),
            'Wrec (储能密度)': within_group_cv['cv_wrec'].mean()
        }

        print(f"基于 {len(within_group_cv)} 个多数据点组:")
        for metric, avg_cv in avg_cv_stats.items():
            print(f"  {metric}: {avg_cv:.2f}%")

        return within_group_cv, avg_cv_stats

class BetweenComponentAnalyzer:
    """不同成分间变异系数分析器"""

    def __init__(self, df):
        """初始化分析器"""
        self.df = df
        self.cv_calculator = VariationCoefficientCalculator()

    def calculate_group_means(self):
        """计算多数据点组的组内平均值"""
        print("\n=== 计算组内平均值 ===")

        # 筛选多数据点组
        multi_point_data = self.df[self.df['group_size'] > 1].copy()

        if len(multi_point_data) == 0:
            print("没有多数据点组")
            return None

        # 计算每个多数据点组的平均值
        group_means = multi_point_data.groupby(['DOI', 'component']).agg({
            'Wrec_norm_10000': 'mean',
            'Wrec_norm_1000': 'mean',
            'η': 'mean',
            'Wrec (J cm-3)': 'mean',
            'group_label': 'first',
            'group_size': 'first'
        }).reset_index()

        print(f"计算了 {len(group_means)} 个多数据点组的平均值")
        return group_means

    def calculate_max_field_values(self):
        """计算多数据点组在最大电场条件下的值"""
        print("\n=== 提取最大电场条件下的值 ===")

        # 筛选多数据点组
        multi_point_data = self.df[self.df['group_size'] > 1].copy()

        if len(multi_point_data) == 0:
            print("没有多数据点组")
            return None

        # 获取每个组在最大电场条件下的值
        def get_max_field_values(group):
            """获取组内最大电场条件下的值"""
            max_field_idx = group['E (kV cm-1)'].idxmax()
            max_field_row = group.loc[max_field_idx]
            return pd.Series({
                'max_field_E': max_field_row['E (kV cm-1)'],
                'max_field_Wrec': max_field_row['Wrec (J cm-3)'],
                'max_field_eta': max_field_row['η'],
                'max_field_Wrec_norm_10000': max_field_row['Wrec_norm_10000'],
                'max_field_Wrec_norm_1000': max_field_row['Wrec_norm_1000'],
                'group_label': max_field_row['group_label'],
                'group_size': max_field_row['group_size']
            })

        group_max_field = multi_point_data.groupby(['DOI', 'component']).apply(
            get_max_field_values, include_groups=False
        ).reset_index()

        print(f"提取了 {len(group_max_field)} 个多数据点组的最大电场条件值")
        return group_max_field

    def calculate_between_component_cv(self, data, data_type=""):
        """计算不同成分间的变异系数"""
        if data is None or len(data) == 0:
            return None

        cv_stats = {
            '10000*Wrec/(E²)': self.cv_calculator.calculate_cv(data['Wrec_norm_10000'] if 'Wrec_norm_10000' in data.columns else data['max_field_Wrec_norm_10000']),
            '1000*Wrec/E': self.cv_calculator.calculate_cv(data['Wrec_norm_1000'] if 'Wrec_norm_1000' in data.columns else data['max_field_Wrec_norm_1000']),
            'η (储能效率)': self.cv_calculator.calculate_cv(data['η'] if 'η' in data.columns else data['max_field_eta']),
            'Wrec (储能密度)': self.cv_calculator.calculate_cv(data['Wrec (J cm-3)'] if 'Wrec (J cm-3)' in data.columns else data['max_field_Wrec'])
        }

        print(f"\n=== 不同成分间变异系数{data_type} ===")
        for metric, cv_value in cv_stats.items():
            print(f"  {metric}: {cv_value:.2f}%")

        return cv_stats

class VariationCoefficientAnalyzer:
    """变异系数综合分析器"""

    def __init__(self, data_file='data2024_copy.xlsx'):
        """初始化分析器"""
        self.data_file = data_file
        self.data_processor = DataProcessor(data_file)
        self.within_analyzer = None
        self.between_analyzer = None
        self.df = None

    def run_analysis(self, output_file='data2024_analysis.xlsx'):
        """运行完整的变异系数分析"""
        print("=== 开始变异系数分析 ===")

        # 1. 加载和预处理数据
        self.df = self.data_processor.load_and_prepare_data()
        self.data_processor.print_basic_statistics()

        # 2. 初始化分析器
        self.within_analyzer = WithinGroupAnalyzer(self.df)
        self.between_analyzer = BetweenComponentAnalyzer(self.df)

        # 3. 计算组内变异系数
        within_cv_results, avg_within_cv = self.within_analyzer.calculate_within_group_cv()

        # 4. 计算组内平均值
        group_means = self.between_analyzer.calculate_group_means()

        # 5. 计算最大电场条件下的值
        group_max_field = self.between_analyzer.calculate_max_field_values()

        # 6. 计算不同成分间变异系数
        between_cv_means = self.between_analyzer.calculate_between_component_cv(
            group_means, "(基于组内平均值)"
        )
        between_cv_max_field = self.between_analyzer.calculate_between_component_cv(
            group_max_field, "(基于最大电场条件)"
        )

        # 7. 保存结果
        self._save_results(output_file, within_cv_results, group_means, group_max_field)

        # 8. 返回分析结果
        return {
            'avg_within_cv': avg_within_cv,
            'between_cv_means': between_cv_means,
            'between_cv_max_field': between_cv_max_field,
            'within_cv_details': within_cv_results,
            'group_means': group_means,
            'group_max_field': group_max_field
        }

    def _save_results(self, output_file, within_cv_results, group_means, group_max_field):
        """保存分析结果到文件"""
        print(f"\n=== 保存分析结果 ===")

        # 保存完整数据
        self.df.to_excel(output_file, index=False)
        print(f"完整数据已保存到: {output_file}")

        # 保存组内变异系数
        if within_cv_results is not None:
            within_cv_file = output_file.replace('.xlsx', '_within_cv.xlsx')
            within_cv_results.to_excel(within_cv_file, index=False)
            print(f"组内变异系数已保存到: {within_cv_file}")

        # 保存组内平均值
        if group_means is not None:
            means_file = output_file.replace('.xlsx', '_group_means.xlsx')
            group_means.to_excel(means_file, index=False)
            print(f"组内平均值已保存到: {means_file}")

        # 保存最大电场条件值
        if group_max_field is not None:
            max_field_file = output_file.replace('.xlsx', '_max_field.xlsx')
            group_max_field.to_excel(max_field_file, index=False)
            print(f"最大电场条件值已保存到: {max_field_file}")


def main():
    """主函数：执行完整的变异系数分析"""
    analyzer = VariationCoefficientAnalyzer('data2024_copy.xlsx')
    results = analyzer.run_analysis('data2024_analysis.xlsx')

    print("\n=== 分析完成 ===")
    return results


def create_grouped_data_with_labels(data_file='data2024_copy.xlsx', output_file='data2024_grouped.xlsx'):
    """
    复制data2024_copy数据，为DOI和成分相同的数据分组并添加标签列，
    计算多数据点组的组内变异系数，以及基于组内平均值的不同成分间变异系数

    Parameters:
    -----------
    data_file : str
        输入数据文件路径
    output_file : str
        输出数据文件路径

    Returns:
    --------
    pd.DataFrame
        包含分组标签和变异系数的数据框

    功能说明:
    --------
    1. 为DOI和成分相同的数据分组
    2. 计算多数据点组的组内变异系数
    3. 计算多数据点组的组内平均值
    4. 提取多数据点组在最大电场条件下的值
    5. 基于组内平均值和最大电场条件分别计算不同成分间的变异系数
    6. 生成三个文件：原始数据文件、组内平均值文件、最大电场条件文件

    新增列说明:
    -----------
    - group_label: 数字分组标签 (0, 1, 2, ...)
    - group_size: 每组包含的数据点数量
    - group_id: 文本分组标识符 (DOI_成分)
    - Wrec_normalized_10000: 10000*Wrec/(E²)
    - Wrec_normalized_1000: 1000*Wrec/E
    - Wrec_over_E_squared: Wrec/(E²)
    - Wrec_over_E: Wrec/E
    - cv_wrec_norm_10000: 10000*Wrec/(E²)的组内变异系数 (%)
    - cv_wrec_norm_1000: 1000*Wrec/E的组内变异系数 (%)
    - cv_wrec_over_e_squared: Wrec/(E²)的组内变异系数 (%)
    - cv_wrec_over_e: Wrec/E的组内变异系数 (%)
    - cv_eta: η(储能效率)的组内变异系数 (%)
    - cv_wrec: Wrec(储能密度)的组内变异系数 (%)
    """
    print(f"正在加载数据文件: {data_file}")

    # 加载原始数据
    df = pd.read_excel(data_file)
    print(f"原始数据形状: {df.shape}")

    # 创建数据副本
    df_grouped = df.copy()

    # 为DOI和成分相同的数据创建分组标签
    # 使用DOI和component的组合作为分组依据
    df_grouped['group_label'] = df_grouped.groupby(['DOI', 'component']).ngroup()

    # 添加更详细的分组信息
    group_info = df_grouped.groupby(['DOI', 'component']).agg({
        'group_label': 'first',
        'E (kV cm-1)': 'count'  # 统计每组的数据点数量
    }).rename(columns={'E (kV cm-1)': 'group_size'}).reset_index()

    # 将分组大小信息合并回原数据
    df_grouped = df_grouped.merge(
        group_info[['DOI', 'component', 'group_size']],
        on=['DOI', 'component'],
        how='left'
    )

    # 创建更直观的分组标识符
    df_grouped['group_id'] = df_grouped['DOI'].astype(str) + '_' + df_grouped['component'].astype(str)

    # 计算各种指标
    df_grouped['Wrec_normalized_10000'] = 10000 * df_grouped['Wrec (J cm-3)'] / (df_grouped['E (kV cm-1)'] ** 2)
    df_grouped['Wrec_normalized_1000'] = 1000 * df_grouped['Wrec (J cm-3)'] / df_grouped['E (kV cm-1)']
    df_grouped['Wrec_over_E_squared'] = df_grouped['Wrec (J cm-3)'] / (df_grouped['E (kV cm-1)'] ** 2)
    df_grouped['Wrec_over_E'] = df_grouped['Wrec (J cm-3)'] / df_grouped['E (kV cm-1)']

    # 为多数据点的组计算组内变异系数
    def calculate_group_cv_stats(group):
        """计算组内各指标的变异系数"""
        if len(group) <= 1:
            return pd.Series({
                'cv_wrec_norm_10000': np.nan,
                'cv_wrec_norm_1000': np.nan,
                'cv_wrec_over_e_squared': np.nan,
                'cv_wrec_over_e': np.nan,
                'cv_eta': np.nan,
                'cv_wrec': np.nan
            })

        def cv(data):
            """计算变异系数"""
            if data.std() == 0 or data.mean() == 0:
                return 0.0
            return (data.std() / data.mean()) * 100

        return pd.Series({
            'cv_wrec_norm_10000': cv(group['Wrec_normalized_10000']),
            'cv_wrec_norm_1000': cv(group['Wrec_normalized_1000']),
            'cv_wrec_over_e_squared': cv(group['Wrec_over_E_squared']),
            'cv_wrec_over_e': cv(group['Wrec_over_E']),
            'cv_eta': cv(group['η']),
            'cv_wrec': cv(group['Wrec (J cm-3)'])
        })

    # 计算每组的变异系数
    group_cv_stats = df_grouped.groupby(['DOI', 'component'], group_keys=False).apply(calculate_group_cv_stats, include_groups=False).reset_index()

    # 将变异系数信息合并回原数据
    df_grouped = df_grouped.merge(
        group_cv_stats,
        on=['DOI', 'component'],
        how='left'
    )

    # 统计分组信息
    total_groups = df_grouped['group_label'].nunique()
    single_point_groups_count = len(df_grouped[df_grouped['group_size'] == 1]['group_label'].unique())
    multi_point_groups_count = len(df_grouped[df_grouped['group_size'] > 1]['group_label'].unique())

    print(f"\n分组统计信息:")
    print(f"总分组数: {total_groups}")
    print(f"单数据点分组数: {single_point_groups_count}")
    print(f"多数据点分组数: {multi_point_groups_count}")
    print(f"平均每组数据点数: {df_grouped['group_size'].mean():.2f}")

    # 显示分组大小分布
    print(f"\n分组大小分布:")
    group_size_dist = df_grouped.drop_duplicates(['DOI', 'component'])['group_size'].value_counts().sort_index()
    for size, count in group_size_dist.head(10).items():
        print(f"  {size}个数据点的分组: {count}个")
    if len(group_size_dist) > 10:
        print(f"  ... (还有{len(group_size_dist) - 10}种分组大小)")

    # 计算多数据点组的变异系数统计
    multi_point_groups_data = df_grouped[df_grouped['group_size'] > 1].drop_duplicates(['DOI', 'component'])

    if len(multi_point_groups_data) > 0:
        print(f"\n多数据点组的变异系数统计:")
        cv_stats = {
            '10000*Wrec/(E²)': multi_point_groups_data['cv_wrec_norm_10000'].dropna(),
            '1000*Wrec/E': multi_point_groups_data['cv_wrec_norm_1000'].dropna(),
            'Wrec/(E²)': multi_point_groups_data['cv_wrec_over_e_squared'].dropna(),
            'Wrec/E': multi_point_groups_data['cv_wrec_over_e'].dropna(),
            'η (储能效率)': multi_point_groups_data['cv_eta'].dropna(),
            'Wrec (储能密度)': multi_point_groups_data['cv_wrec'].dropna()
        }

        for metric_name, cv_values in cv_stats.items():
            if len(cv_values) > 0:
                print(f"  {metric_name}:")
                print(f"    平均变异系数: {cv_values.mean():.2f}%")
                print(f"    中位数变异系数: {cv_values.median():.2f}%")
                print(f"    变异系数范围: {cv_values.min():.2f}% - {cv_values.max():.2f}%")
                print(f"    有效组数: {len(cv_values)}")
            else:
                print(f"  {metric_name}: 无有效数据")

    # 显示一些示例分组
    print(f"\n示例分组 (前5个多数据点分组):")
    multi_point_examples = df_grouped[df_grouped['group_size'] > 1].groupby(['group_label', 'DOI', 'component']).size().reset_index(name='actual_count').head(5)
    for _, row in multi_point_examples.iterrows():
        group_data = df_grouped[df_grouped['group_label'] == row['group_label']]
        group_cv_data = group_data.iloc[0]  # 获取该组的变异系数信息
        print(f"  分组 {row['group_label']}: DOI={row['DOI'][:20]}..., 成分={row['component'][:30]}..., 数据点数={row['actual_count']}")
        print(f"    电场范围: {group_data['E (kV cm-1)'].min():.1f} - {group_data['E (kV cm-1)'].max():.1f} kV/cm")
        print(f"    变异系数 - Wrec/(E²): {group_cv_data['cv_wrec_over_e_squared']:.2f}%, Wrec/E: {group_cv_data['cv_wrec_over_e']:.2f}%, η: {group_cv_data['cv_eta']:.2f}%")

    # 计算多数据点组的组内平均值和最大电场条件下的值，然后计算不同成分间的变异系数
    multi_point_groups_data = df_grouped[df_grouped['group_size'] > 1]

    if len(multi_point_groups_data) > 0:
        # 计算每个多数据点组的平均值
        group_means = multi_point_groups_data.groupby(['DOI', 'component']).agg({
            'Wrec_normalized_10000': 'mean',
            'Wrec_normalized_1000': 'mean',
            'η': 'mean',
            'Wrec (J cm-3)': 'mean',
            'group_label': 'first',
            'group_size': 'first'
        }).reset_index()

        # 计算每个多数据点组在最大电场条件下的值
        def get_max_field_values(group):
            """获取组内最大电场条件下的值"""
            max_field_idx = group['E (kV cm-1)'].idxmax()
            max_field_row = group.loc[max_field_idx]
            return pd.Series({
                'max_field_E': max_field_row['E (kV cm-1)'],
                'max_field_Wrec': max_field_row['Wrec (J cm-3)'],
                'max_field_eta': max_field_row['η'],
                'max_field_Wrec_norm_10000': max_field_row['Wrec_normalized_10000'],
                'max_field_Wrec_norm_1000': max_field_row['Wrec_normalized_1000'],
                'group_label': max_field_row['group_label'],
                'group_size': max_field_row['group_size']
            })

        group_max_field = multi_point_groups_data.groupby(['DOI', 'component']).apply(
            get_max_field_values, include_groups=False
        ).reset_index()

        # 计算不同成分间的变异系数
        def between_component_cv(data):
            """计算不同成分间的变异系数"""
            if data.std() == 0 or data.mean() == 0:
                return 0.0
            return (data.std() / data.mean()) * 100

        # 基于组内平均值的变异系数
        between_cv_means = {
            '10000*Wrec/(E²)': between_component_cv(group_means['Wrec_normalized_10000']),
            '1000*Wrec/E': between_component_cv(group_means['Wrec_normalized_1000']),
            'η (储能效率)': between_component_cv(group_means['η']),
            'Wrec (储能密度)': between_component_cv(group_means['Wrec (J cm-3)'])
        }

        # 基于最大电场条件下的变异系数
        between_cv_max_field = {
            '10000*Wrec/(E²)': between_component_cv(group_max_field['max_field_Wrec_norm_10000']),
            '1000*Wrec/E': between_component_cv(group_max_field['max_field_Wrec_norm_1000']),
            'η (储能效率)': between_component_cv(group_max_field['max_field_eta']),
            'Wrec (储能密度)': between_component_cv(group_max_field['max_field_Wrec'])
        }

        print(f"\n=== 不同成分间变异系数分析 ===")
        print(f"基于 {len(group_means)} 个多数据点组计算:")

        print(f"\n1. 基于组内平均值:")
        for metric_name, cv_value in between_cv_means.items():
            print(f"  {metric_name}: {cv_value:.2f}%")

        print(f"\n2. 基于最大电场条件下的值:")
        for metric_name, cv_value in between_cv_max_field.items():
            print(f"  {metric_name}: {cv_value:.2f}%")

        # 保存数据
        group_means_file = output_file.replace('.xlsx', '_group_means.xlsx')
        group_means.to_excel(group_means_file, index=False)
        print(f"\n已保存组内平均值数据到: {group_means_file}")

        group_max_field_file = output_file.replace('.xlsx', '_max_field.xlsx')
        group_max_field.to_excel(group_max_field_file, index=False)
        print(f"已保存最大电场条件数据到: {group_max_field_file}")

        # 将不同成分间变异系数添加到返回结果中
        df_grouped.attrs['between_component_cv_means'] = between_cv_means
        df_grouped.attrs['between_component_cv_max_field'] = between_cv_max_field
        df_grouped.attrs['group_means'] = group_means
        df_grouped.attrs['group_max_field'] = group_max_field

    # 保存带标签的数据
    df_grouped.to_excel(output_file, index=False)
    print(f"\n已保存分组数据到: {output_file}")
    print(f"新增列: group_label, group_size, group_id")
    print(f"归一化指标列: Wrec_normalized_10000, Wrec_normalized_1000, Wrec_over_E_squared, Wrec_over_E")
    print(f"变异系数列: cv_wrec_norm_10000, cv_wrec_norm_1000, cv_wrec_over_e_squared, cv_wrec_over_e, cv_eta, cv_wrec")

    if len(multi_point_groups_data) > 0:
        print(f"\n生成的分析文件:")
        print(f"  - {output_file}: 完整数据")
        print(f"  - {group_means_file}: 组内平均值")
        print(f"  - {group_max_field_file}: 最大电场条件下的值")

    return df_grouped


if __name__ == "__main__":
    main()

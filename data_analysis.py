import pandas as pd
import numpy as np


class CeramicDataAnalyzer:
    """陶瓷储能材料数据分析器"""

    def __init__(self, data_file='data2024_copy.xlsx'):
        """初始化分析器并加载数据"""
        self.data_file = data_file
        self.df_raw = None
        self.df_clean = None
        self.load_data()

    def load_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        self.df_raw = pd.read_excel(self.data_file)
        print(f"原始数据形状: {self.df_raw.shape}")
        print("缺失值情况:")
        print(self.df_raw.isnull().sum())

        # 删除电场强度缺失的行
        self.df_clean = self.df_raw.dropna(subset=['E (kV cm-1)']).copy()
        print(f"\n清理后数据形状: {self.df_clean.shape}")

        # 计算归一化储能密度
        self.df_clean['Wrec_normalized'] = self.calculate_normalized_wrec(
            self.df_clean['Wrec (J cm-3)'],
            self.df_clean['E (kV cm-1)']
        )

        self.print_basic_statistics()

    def print_basic_statistics(self):
        """打印基本统计信息"""
        print("\n数据基本统计:")
        print(self.df_clean[['component', 'E (kV cm-1)', 'Wrec (J cm-3)', 'η', 'Wrec_normalized']].describe())

        print(f"\n总共有 {self.df_clean['component'].nunique()} 种不同成分")
        print(f"总共有 {self.df_clean['DOI'].nunique()} 篇不同文献")

        # 按DOI和成分分组，查看每组的数据量
        component_doi_groups = self.df_clean.groupby(['DOI', 'component']).size().reset_index(name='count')
        print(f"\n按文献和成分分组后有 {len(component_doi_groups)} 个组合")
        print("每组数据量分布:")
        print(component_doi_groups['count'].describe())


class StatisticalCalculator:
    """统计计算工具类"""

    @staticmethod
    def coefficient_of_variation(data):
        """计算变异系数 (CV = std/mean * 100%)"""
        if len(data) <= 1 or data.mean() == 0:
            return np.nan
        return (data.std() / data.mean()) * 100

    @staticmethod
    def calculate_normalized_wrec(wrec, electric_field):
        """计算归一化储能密度: 10000*Wrec/(E*E)"""
        return 10000 * wrec / (electric_field ** 2)

    @staticmethod
    def basic_statistics(data):
        """计算基本统计量"""
        return {
            'mean': data.mean(),
            'std': data.std(),
            'count': len(data),
            'cv': StatisticalCalculator.coefficient_of_variation(data)
        }

    @staticmethod
    def analyze_within_group_variation(group):
        """分析组内变异"""
        if len(group) <= 1:
            return pd.Series({
                'wrec_norm_cv': np.nan,
                'eta_cv': np.nan,
                'count': len(group),
                'e_range': np.nan
            })

        return pd.Series({
            'wrec_norm_cv': StatisticalCalculator.coefficient_of_variation(group['Wrec_normalized']),
            'eta_cv': StatisticalCalculator.coefficient_of_variation(group['η']),
            'count': len(group),
            'e_range': group['E (kV cm-1)'].max() - group['E (kV cm-1)'].min()
        })

class ComponentAnalyzer:
    """成分差异分析器"""

    def __init__(self, df_clean):
        self.df_clean = df_clean
        self.component_stats = None
        self.within_group_stats = None
        self.within_group_stats_filtered = None

    def analyze_between_components(self):
        """分析不同成分间的差异（跨成分比较）"""
        print("\n" + "="*60)
        print("1. 不同成分间的差异分析")
        print("="*60)

        # 计算每种成分的平均值
        self.component_stats = self.df_clean.groupby('component').agg({
            'Wrec_normalized': ['mean', 'std', 'count', StatisticalCalculator.coefficient_of_variation],
            'η': ['mean', 'std', 'count', StatisticalCalculator.coefficient_of_variation]
        }).round(4)

        self.component_stats.columns = ['Wrec_norm_mean', 'Wrec_norm_std', 'Wrec_norm_count', 'Wrec_norm_cv',
                                      'eta_mean', 'eta_std', 'eta_count', 'eta_cv']

        print("不同成分的统计信息:")
        print(self.component_stats.head(10))

        # 计算所有成分间的变异系数
        all_components_wrec_cv = StatisticalCalculator.coefficient_of_variation(self.component_stats['Wrec_norm_mean'])
        all_components_eta_cv = StatisticalCalculator.coefficient_of_variation(self.component_stats['eta_mean'])

        print(f"\n所有成分间 Wrec_normalized 的变异系数: {all_components_wrec_cv:.2f}%")
        print(f"所有成分间 η 的变异系数: {all_components_eta_cv:.2f}%")

        return all_components_wrec_cv, all_components_eta_cv

    def analyze_within_components(self):
        """分析相同成分在不同电场下的差异（组内比较）"""
        print("\n" + "="*60)
        print("2. 相同成分在不同电场下的差异分析")
        print("="*60)

        # 按DOI和成分分组分析
        self.within_group_stats = self.df_clean.groupby(['DOI', 'component'], group_keys=False).apply(
            StatisticalCalculator.analyze_within_group_variation
        ).reset_index()

        # 过滤掉只有一个数据点的组
        self.within_group_stats_filtered = self.within_group_stats[self.within_group_stats['count'] > 1].copy()

        print(f"有多个数据点的组合数量: {len(self.within_group_stats_filtered)}")
        print(f"这些组合的数据点数量分布:")
        print(self.within_group_stats_filtered['count'].describe())

        # 计算组内变异系数的统计
        self.print_within_group_statistics()

        return self.within_group_stats_filtered

    def print_within_group_statistics(self):
        """打印组内变异系数统计"""
        print(f"\n相同成分不同电场下的变异系数统计:")
        print(f"Wrec_normalized 组内变异系数:")
        print(f"  平均值: {self.within_group_stats_filtered['wrec_norm_cv'].mean():.2f}%")
        print(f"  中位数: {self.within_group_stats_filtered['wrec_norm_cv'].median():.2f}%")
        print(f"  标准差: {self.within_group_stats_filtered['wrec_norm_cv'].std():.2f}%")

        print(f"\nη 组内变异系数:")
        print(f"  平均值: {self.within_group_stats_filtered['eta_cv'].mean():.2f}%")
        print(f"  中位数: {self.within_group_stats_filtered['eta_cv'].median():.2f}%")
        print(f"  标准差: {self.within_group_stats_filtered['eta_cv'].std():.2f}%")

class ResultAnalyzer:
    """结果分析和比较器"""

    def __init__(self, df_clean):
        self.df_clean = df_clean

    def compare_variations(self, all_components_wrec_cv, all_components_eta_cv, within_group_stats_filtered):
        """比较不同类型的变异系数"""
        print("\n" + "="*60)
        print("3. 比较分析结果")
        print("="*60)

        print("变异系数比较 (变异系数越大表示差异越大):")
        print(f"1. 不同成分间 Wrec_normalized 变异系数: {all_components_wrec_cv:.2f}%")
        print(f"2. 相同成分不同电场下 Wrec_normalized 平均变异系数: {within_group_stats_filtered['wrec_norm_cv'].mean():.2f}%")
        print(f"   差异倍数: {all_components_wrec_cv / within_group_stats_filtered['wrec_norm_cv'].mean():.2f}倍")

        print(f"\n1. 不同成分间 η 变异系数: {all_components_eta_cv:.2f}%")
        print(f"2. 相同成分不同电场下 η 平均变异系数: {within_group_stats_filtered['eta_cv'].mean():.2f}%")
        print(f"   差异倍数: {all_components_eta_cv / within_group_stats_filtered['eta_cv'].mean():.2f}倍")

        return self.generate_conclusions(all_components_wrec_cv, all_components_eta_cv, within_group_stats_filtered)

    def generate_conclusions(self, all_components_wrec_cv, all_components_eta_cv, within_group_stats_filtered):
        """生成分析结论"""
        print("\n" + "="*60)
        print("4. 结论")
        print("="*60)

        wrec_ratio = all_components_wrec_cv / within_group_stats_filtered['wrec_norm_cv'].mean()
        eta_ratio = all_components_eta_cv / within_group_stats_filtered['eta_cv'].mean()

        print("基于变异系数的分析结果:")
        print(f"1. 对于 10000*Wrec/(E*E):")
        if wrec_ratio > 2:
            print(f"   ✓ 不同成分间差异显著大于相同成分不同电场下的差异 (差异倍数: {wrec_ratio:.2f})")
        else:
            print(f"   ✗ 不同成分间差异与相同成分不同电场下的差异相当 (差异倍数: {wrec_ratio:.2f})")

        print(f"2. 对于 η (储能效率):")
        if eta_ratio > 2:
            print(f"   ✓ 不同成分间差异显著大于相同成分不同电场下的差异 (差异倍数: {eta_ratio:.2f})")
        else:
            print(f"   ✗ 不同成分间差异与相同成分不同电场下的差异相当 (差异倍数: {eta_ratio:.2f})")

        self.print_supplementary_statistics(within_group_stats_filtered)

        return {
            'wrec_ratio': wrec_ratio,
            'eta_ratio': eta_ratio,
            'all_components_wrec_cv': all_components_wrec_cv,
            'all_components_eta_cv': all_components_eta_cv,
            'within_wrec_cv_mean': within_group_stats_filtered['wrec_norm_cv'].mean(),
            'within_eta_cv_mean': within_group_stats_filtered['eta_cv'].mean()
        }

    def print_supplementary_statistics(self, within_group_stats_filtered):
        """打印补充统计信息"""
        print(f"\n补充统计信息:")
        print(f"- 分析了 {self.df_clean['component'].nunique()} 种不同成分")
        print(f"- 来自 {self.df_clean['DOI'].nunique()} 篇文献")
        print(f"- 有 {len(within_group_stats_filtered)} 个成分-文献组合有多个电场数据点")
        print(f"- 电场范围: {self.df_clean['E (kV cm-1)'].min():.1f} - {self.df_clean['E (kV cm-1)'].max():.1f} kV/cm")




def main():
    """主函数：执行完整的陶瓷材料数据分析"""
    print("开始陶瓷储能材料数据分析...")

    # 1. 初始化数据分析器
    data_analyzer = CeramicDataAnalyzer('data2024_copy.xlsx')

    # 2. 初始化成分分析器
    component_analyzer = ComponentAnalyzer(data_analyzer.df_clean)

    # 3. 分析不同成分间的差异
    all_components_wrec_cv, all_components_eta_cv = component_analyzer.analyze_between_components()

    # 4. 分析相同成分在不同电场下的差异
    within_group_stats_filtered = component_analyzer.analyze_within_components()

    # 5. 比较分析结果
    result_analyzer = ResultAnalyzer(data_analyzer.df_clean)
    analysis_results = result_analyzer.compare_variations(
        all_components_wrec_cv, all_components_eta_cv, within_group_stats_filtered
    )

    print("\n分析完成！")
    return analysis_results


# 添加缺失的方法到CeramicDataAnalyzer类
def calculate_normalized_wrec(wrec, electric_field):
    """计算归一化储能密度: 10000*Wrec/(E*E)"""
    return 10000 * wrec / (electric_field ** 2)

# 将方法添加到CeramicDataAnalyzer类
CeramicDataAnalyzer.calculate_normalized_wrec = staticmethod(calculate_normalized_wrec)


def create_grouped_data_with_labels(data_file='data2024_copy.xlsx', output_file='data2024_grouped.xlsx'):
    """
    复制data2024_copy数据，为DOI和成分相同的数据分组并添加标签列

    Parameters:
    -----------
    data_file : str
        输入数据文件路径
    output_file : str
        输出数据文件路径

    Returns:
    --------
    pd.DataFrame
        包含分组标签的数据框
    """
    print(f"正在加载数据文件: {data_file}")

    # 加载原始数据
    df = pd.read_excel(data_file)
    print(f"原始数据形状: {df.shape}")

    # 创建数据副本
    df_grouped = df.copy()

    # 为DOI和成分相同的数据创建分组标签
    # 使用DOI和component的组合作为分组依据
    df_grouped['group_label'] = df_grouped.groupby(['DOI', 'component']).ngroup()

    # 添加更详细的分组信息
    group_info = df_grouped.groupby(['DOI', 'component']).agg({
        'group_label': 'first',
        'E (kV cm-1)': 'count'  # 统计每组的数据点数量
    }).rename(columns={'E (kV cm-1)': 'group_size'}).reset_index()

    # 将分组大小信息合并回原数据
    df_grouped = df_grouped.merge(
        group_info[['DOI', 'component', 'group_size']],
        on=['DOI', 'component'],
        how='left'
    )

    # 创建更直观的分组标识符
    df_grouped['group_id'] = df_grouped['DOI'].astype(str) + '_' + df_grouped['component'].astype(str)

    # 统计分组信息
    total_groups = df_grouped['group_label'].nunique()
    single_point_groups_count = len(df_grouped[df_grouped['group_size'] == 1]['group_label'].unique())
    multi_point_groups_count = len(df_grouped[df_grouped['group_size'] > 1]['group_label'].unique())

    print(f"\n分组统计信息:")
    print(f"总分组数: {total_groups}")
    print(f"单数据点分组数: {single_point_groups_count}")
    print(f"多数据点分组数: {multi_point_groups_count}")
    print(f"平均每组数据点数: {df_grouped['group_size'].mean():.2f}")

    # 显示分组大小分布
    print(f"\n分组大小分布:")
    group_size_dist = df_grouped.drop_duplicates(['DOI', 'component'])['group_size'].value_counts().sort_index()
    for size, count in group_size_dist.head(10).items():
        print(f"  {size}个数据点的分组: {count}个")
    if len(group_size_dist) > 10:
        print(f"  ... (还有{len(group_size_dist) - 10}种分组大小)")

    # 显示一些示例分组
    print(f"\n示例分组 (前5个多数据点分组):")
    multi_point_examples = df_grouped[df_grouped['group_size'] > 1].groupby(['group_label', 'DOI', 'component']).size().reset_index(name='actual_count').head(5)
    for _, row in multi_point_examples.iterrows():
        group_data = df_grouped[df_grouped['group_label'] == row['group_label']]
        print(f"  分组 {row['group_label']}: DOI={row['DOI'][:20]}..., 成分={row['component'][:30]}..., 数据点数={row['actual_count']}")
        print(f"    电场范围: {group_data['E (kV cm-1)'].min():.1f} - {group_data['E (kV cm-1)'].max():.1f} kV/cm")

    # 保存带标签的数据
    df_grouped.to_excel(output_file, index=False)
    print(f"\n已保存分组数据到: {output_file}")
    print(f"新增列: group_label (数字标签), group_size (分组大小), group_id (文本标识符)")

    return df_grouped


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试不同成分间变异系数计算功能
"""

import pandas as pd
import numpy as np
from data_analysis import create_grouped_data_with_labels

def test_between_component_cv():
    """测试不同成分间变异系数计算功能"""
    print("开始测试不同成分间变异系数计算功能...")
    
    try:
        # 调用完整的分组函数
        df_grouped = create_grouped_data_with_labels(
            data_file='data2024_copy.xlsx',
            output_file='data2024_full_analysis.xlsx'
        )
        
        print(f"\n=== 测试结果验证 ===")
        
        # 检查是否生成了组内平均值文件
        try:
            group_means_df = pd.read_excel('data2024_full_analysis_group_means.xlsx')
            print(f"✓ 成功生成组内平均值文件")
            print(f"组内平均值数据形状: {group_means_df.shape}")
            print(f"组内平均值文件列名: {list(group_means_df.columns)}")
        except FileNotFoundError:
            print("✗ 未找到组内平均值文件")
            return None
        
        # 验证不同成分间变异系数是否存储在属性中
        if hasattr(df_grouped, 'attrs') and 'between_component_cv' in df_grouped.attrs:
            between_cv = df_grouped.attrs['between_component_cv']
            print(f"\n=== 不同成分间变异系数验证 ===")
            for metric, cv_value in between_cv.items():
                print(f"  {metric}: {cv_value:.2f}%")
        else:
            print("✗ 未找到不同成分间变异系数数据")
        
        # 手动验证计算结果
        print(f"\n=== 手动验证计算 ===")
        
        # 筛选多数据点组
        multi_point_data = df_grouped[df_grouped['group_size'] > 1].copy()
        # 清除attrs以避免pandas错误
        multi_point_data.attrs = {}

        # 计算组内平均值
        manual_group_means = multi_point_data.groupby(['DOI', 'component']).agg({
            'Wrec_normalized_10000': 'mean',
            'Wrec_normalized_1000': 'mean',
            'η': 'mean',
            'Wrec (J cm-3)': 'mean'
        }).reset_index()
        
        print(f"多数据点组数量: {len(manual_group_means)}")
        print(f"组内平均值示例:")
        print(manual_group_means[['DOI', 'component', 'Wrec_normalized_10000', 'η']].head())
        
        # 手动计算不同成分间变异系数
        def manual_cv(data):
            if data.std() == 0 or data.mean() == 0:
                return 0.0
            return (data.std() / data.mean()) * 100
        
        manual_between_cv = {
            '10000*Wrec/(E²)': manual_cv(manual_group_means['Wrec_normalized_10000']),
            '1000*Wrec/E': manual_cv(manual_group_means['Wrec_normalized_1000']),
            'η (储能效率)': manual_cv(manual_group_means['η']),
            'Wrec (储能密度)': manual_cv(manual_group_means['Wrec (J cm-3)'])
        }
        
        print(f"\n手动计算的不同成分间变异系数:")
        for metric, cv_value in manual_between_cv.items():
            print(f"  {metric}: {cv_value:.2f}%")
        
        # 比较手动计算和函数计算的结果
        if hasattr(df_grouped, 'attrs') and 'between_component_cv' in df_grouped.attrs:
            stored_cv = df_grouped.attrs['between_component_cv']
            print(f"\n=== 计算结果对比 ===")
            tolerance = 0.01
            all_correct = True
            
            for metric in manual_between_cv.keys():
                manual_val = manual_between_cv[metric]
                stored_val = stored_cv[metric]
                diff = abs(manual_val - stored_val)
                
                if diff < tolerance:
                    print(f"✓ {metric}: 手动={manual_val:.2f}%, 存储={stored_val:.2f}% (差异={diff:.4f})")
                else:
                    print(f"✗ {metric}: 手动={manual_val:.2f}%, 存储={stored_val:.2f}% (差异={diff:.4f})")
                    all_correct = False
            
            if all_correct:
                print("✓ 所有变异系数计算正确")
            else:
                print("✗ 部分变异系数计算有误")
        
        # 显示一些统计信息
        print(f"\n=== 统计摘要 ===")
        print(f"总数据点: {len(df_grouped)}")
        print(f"总分组数: {df_grouped['group_label'].nunique()}")
        print(f"多数据点组数: {len(manual_group_means)}")
        print(f"单数据点组数: {df_grouped['group_label'].nunique() - len(manual_group_means)}")
        
        # 显示组内平均值的分布
        print(f"\n组内平均值分布:")
        for metric in ['Wrec_normalized_10000', 'η', 'Wrec (J cm-3)']:
            data = manual_group_means[metric]
            print(f"  {metric}: 均值={data.mean():.4f}, 标准差={data.std():.4f}, 范围=[{data.min():.4f}, {data.max():.4f}]")
        
        print(f"\n=== 测试完成 ===")
        print(f"生成的文件:")
        print(f"  - data2024_full_analysis.xlsx (完整数据)")
        print(f"  - data2024_full_analysis_group_means.xlsx (组内平均值)")
        
        return df_grouped, group_means_df
        
    except FileNotFoundError:
        print("错误: 找不到 data2024_copy.xlsx 文件")
        return None, None
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    result_df, means_df = test_between_component_cv()
